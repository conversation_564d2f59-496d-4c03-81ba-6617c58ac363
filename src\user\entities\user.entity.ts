import { ApiProperty } from "@nestjs/swagger";
import { IsDate, IsString } from "class-validator";

export class User {

  @ApiProperty()
  @IsString()
  id: string;

  @ApiProperty()
  @IsString()
  fullname: string;

  @ApiProperty()
  @IsString()
  password: string;

  @ApiProperty()
  @IsString()
  email: string;

  @ApiProperty()
  @IsString()
  type: string;

  @ApiProperty()
  @IsString()
  cellphone_number?: string;

  @ApiProperty()
  @IsString()
  cellphone_number_aux?: string;

  @ApiProperty()
  @IsString()
  gender: string;

  @ApiProperty()
  @IsDate()
  date_birth: Date;

  @ApiProperty()
  @IsString()
  cgc: string;

  @ApiProperty()
  @IsString()
  avatar_url: string;

  @ApiProperty()
  @IsString()
  date_birth_BR: string;

  @ApiProperty()
  @IsDate()
  last_online: Date;

  @ApiProperty()
  @IsString()
  password_code: string;

  @ApiProperty()
  @IsString()
  asaas_id: string;

  @ApiProperty()
  @IsString()
  picture: string;
}
