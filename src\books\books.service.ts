import { BadRequestException, Injectable, UnauthorizedException } from '@nestjs/common';
import { xata } from 'src/helpers/xata';
import { CreateBookDto } from './dto/create-book.dto';
import { UpdateBookDto } from './dto/update-book.dto';
import { validateBearer } from 'src/helpers/utils';

@Injectable()
export class BooksService {
    async create(bearer: string, createBookDto: CreateBookDto) {
        const validate = await validateBearer(bearer)
        if (!validate.success) throw new UnauthorizedException()
        if (!validate.usuario) throw new UnauthorizedException()
        if (!['A', 'C'].includes(validate.usuario.type)) throw new UnauthorizedException()

        const { 
            titulo,
            idioma,
            dt_publicacao,
            nro_paginas,
            autor,
            editora,
            resumo,
            link,
            url_imagem,
        } = createBookDto
    
        if (!titulo) throw new BadRequestException('Titulo deve ser informado')
        if (!idioma) throw new BadRequestException('Idioma deve ser informado')
        if (!autor) throw new BadRequestException('Autor deve ser informado')
        if (!resumo) throw new BadRequestException('Resumo deve ser informado')
        if (!link) throw new BadRequestException('Link deve ser informado')
        if (!url_imagem) throw new BadRequestException('URL da imagem deve ser informado')
    
        const recordNew = await xata.db.Livros.create({
            titulo: titulo.trim(),
            idioma: idioma.trim(),
            dt_publicacao: new Date(dt_publicacao).toISOString(),
            nro_paginas: Number(nro_paginas),
            autor: autor.trim(),
            editora: editora.trim(),
            resumo: resumo.trim(),
            link: link.trim(),
            url_imagem: url_imagem.trim(),
        })
    
        if (!recordNew) {
          throw new BadRequestException('Erro ao cadastrar')
        }
    
        const recordCreated = await xata.db.Livros
          .select([
            'titulo',
            'idioma',
            'dt_publicacao',
            'nro_paginas',
            'autor',
            'editora',
            'resumo',
            'link',
            'url_imagem'
          ])
          .filter({ id: recordNew.id })
          .getFirst()
    
        if (!recordCreated) {
          throw new BadRequestException('Erro ao cadastrar')
        }
    
        return recordCreated
    }

    async findAll(titulo_autor: string) {
    
        const records = await xata.db.Livros
        .select([
            'titulo',
            'idioma',
            'dt_publicacao',
            'nro_paginas',
            'autor',
            'editora',
            'resumo',
            'link',
            'url_imagem'
        ])
        .filter({
          $any: [
            {
              titulo: {
                $iContains: titulo_autor
              }
            },
            {
              autor: {
                $iContains: titulo_autor
              }
            }
          ]
        })
        .getAll()
    
        records.sort((a: any, b: any) => {
          if (a.titulo < b.titulo) return 1
          else return -1
        })
    
        return records
    }

    async findOne(id: string) {
        const records = await xata.db.Livros
        .select([
            'titulo',
            'idioma',
            'dt_publicacao',
            'nro_paginas',
            'autor',
            'editora',
            'resumo',
            'link',
            'url_imagem'
        ])
        .filter({
          id: id
        }).getFirst()
        console.log("🤬 ~ BooksService ~ findOne ~ records:", records)
        return records
    }

    async getLink(id: string) {
        const records = await xata.db.Livros
        .select([
            'acessos',
            'link'
        ])
        .filter({
          id: id
        }).getFirst()

        const record = await xata.db.Livros.update(id, {
          acessos: records.acessos + 1
        })

        return records.link
    }

    async update(bearer: string, id: string, updateBookDto: UpdateBookDto) {
        const validate = await validateBearer(bearer)
        if (!validate.success) throw new UnauthorizedException()
        if (!validate.usuario) throw new UnauthorizedException()
        if (!['A', 'C'].includes(validate.usuario.type)) throw new UnauthorizedException()

        const { 
            titulo,
            idioma,
            dt_publicacao,
            nro_paginas,
            autor,
            editora,
            resumo,
            link,
            url_imagem
        } = updateBookDto

        if (!titulo) throw new BadRequestException('Titulo deve ser informado')
        if (!idioma) throw new BadRequestException('Idioma deve ser informado')
        if (!autor) throw new BadRequestException('Autor deve ser informado')
        if (!resumo) throw new BadRequestException('Resumo deve ser informado')
        if (!link) throw new BadRequestException('Link deve ser informado')
        if (!url_imagem) throw new BadRequestException('URL da Imagem deve ser informado')
    
        const record = await xata.db.Livros.update(id, updateBookDto)
        return record
    }

    async remove(bearer: string, id: string) {
        const validate = await validateBearer(bearer)
        if (!validate.success) throw new UnauthorizedException()
        if (!validate.usuario) throw new UnauthorizedException()
        if (!['A', 'C'].includes(validate.usuario.type)) throw new UnauthorizedException()
    
        const record = await xata.db.Users.delete(id)
        return record
    }
}
