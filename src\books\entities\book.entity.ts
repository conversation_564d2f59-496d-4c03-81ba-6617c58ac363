import { ApiProperty } from "@nestjs/swagger";
import { <PERSON>N<PERSON>ber, IsOptional, IsString } from "class-validator";

export class Book {

  @ApiProperty()
  @IsString()
  id: string;

  @ApiProperty()
  @IsString()
  titulo: string;

  @ApiProperty()
  @IsString()
  idioma: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  dt_publicacao?: string;

  @ApiProperty()
  @IsNumber()
  @IsOptional()
  nro_paginas?: number;

  @ApiProperty()
  @IsString()
  autor: string;

  @ApiProperty()
  @IsString()
  resumo: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  editora?: string;

  @ApiProperty()
  @IsString()
  link: string;

  @ApiProperty()
  @IsString()
  url_imagem: string;
}
