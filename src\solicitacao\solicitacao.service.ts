import { BadRequestException, Injectable, UnauthorizedException } from '@nestjs/common';
import { CreateSolicitacaoDto } from './dto/create-solicitacao.dto';
import { UpdateSolicitacaoDto } from './dto/update-solicitacao.dto';
import { xata } from 'src/helpers/xata';
import { isValidDate, validateBearer } from 'src/helpers/utils';
import { OrcarSolicitacaoDto } from './dto/orcar-solicitacao.dto';
import { asaas } from 'src/helpers/asaas';
import { WebhookDto } from './dto/webhook.dto';
import { EntregaSolicitacaoDto } from './dto/entrega-solicitacao.dto';
import { sendMailNewRequest } from 'src/helpers/mail';

@Injectable()
export class SolicitacaoService {
  async create(bearer: string, createSolicitacaoDto: CreateSolicitacaoDto) {
    const validate = await validateBearer(bearer)
    if (!validate.success) throw new UnauthorizedException()
    if (!validate.usuario) throw new UnauthorizedException()
    
    const { titulo, tipo, user_id } = createSolicitacaoDto

    if (!titulo) throw new BadRequestException('Informe o título')
    if (!user_id) throw new BadRequestException('Informe o usuário')
    if (!tipo) throw new BadRequestException('Informe o tipo')
    // if (!variaveis) throw new BadRequestException('Informe as variáveis')
    if (!['P', 'R', 'O'].includes(tipo)) throw new BadRequestException('Tipo de solicitação incorreto.')

    delete createSolicitacaoDto.user_id

    const solicitacao = {...{
      status: 'AGUARDANDO_ANALISTA',
      usuario: user_id || validate.usuario.id
    }, ...createSolicitacaoDto};

    const record = await xata.db.Solicitacoes.create(solicitacao)
    const email = await sendMailNewRequest()
    return 'Solicitação cadastrada com sucesso'
  }

  async reabrir(bearer: string, id: string) {
    const validate = await validateBearer(bearer)
    if (!validate.success) throw new UnauthorizedException()
    if (!validate.usuario) throw new UnauthorizedException()

    const solicitacao = {
      status: 'AGUARDANDO_ANALISTA',
      colaborador: null
    }

    const record = await xata.db.Solicitacoes.update(id, solicitacao)
    return 'Solicitação reaberta com sucesso'
  }

  async findAll(bearer: string) {
    const validate = await validateBearer(bearer)
    if (!validate.success) throw new UnauthorizedException()
    if (!validate.usuario) throw new UnauthorizedException()

    if (['A', 'C'].includes(validate.usuario.type)){
      const solicitacoes = await xata.db.Solicitacoes.select(['*', 'colaborador.*', 'usuario.*']).getAll()
      return solicitacoes
    } else {
      const solicitacoes = await xata.db.Solicitacoes
      .select(['*', 'colaborador.*', 'usuario.*'])
      .filter({
        usuario: validate.usuario.id
      })
      .getAll()
      const Solicitacoes = []
      for (let solicitacao of solicitacoes){
        const payment = await xata.db.Payments.filter({
          "solicitacao.id": solicitacao.id
        }).getAll()
        const pagamento = {
          pagamento: payment
        }
        Solicitacoes.push({...pagamento, ...solicitacao})
      }
      return Solicitacoes
    }
  }

  async findOne(bearer: string, id: string) {
    const validate = await validateBearer(bearer)
    if (!validate.success) throw new UnauthorizedException()
    if (!validate.usuario) throw new UnauthorizedException()

    if (!id) throw new BadRequestException('Informe o id da solicitação')

    const solicitacao = await xata.db.Solicitacoes
      .select(['*', 'colaborador.*', 'usuario.*'])
      .filter({
        id: id
      })
      .getFirst()

    if (!solicitacao) throw new BadRequestException('Solicitação não encontrada')

    if (!['A', 'C'].includes(validate.usuario.type)){
      if (solicitacao.usuario.id != validate.usuario.id) throw new BadRequestException('Você não tem permissão para acessar esta solicitação')
    }

    const payment = await xata.db.Payments.filter({
      "solicitacao.id": solicitacao.id
    }).getAll()
    const pagamento = {
      pagamento: payment
    }

    const Solicitacao = {...solicitacao, ...pagamento}

    return Solicitacao;
  }

  async update(bearer: string, id: string, updateSolicitacaoDto: UpdateSolicitacaoDto) {
    const validate = await validateBearer(bearer)
    if (!validate.success) throw new UnauthorizedException()
    if (!validate.usuario) throw new UnauthorizedException()

    if (!id) throw new BadRequestException('Informe o id da solicitação')

    const solicitacao = await xata.db.Solicitacoes
      .filter({
        id: id
      })
      .getFirst()

    if (!solicitacao) throw new BadRequestException('Solicitação não encontrada')

    if (!['A', 'C'].includes(validate.usuario.type)){
      if (solicitacao.usuario.id != validate.usuario.id) throw new BadRequestException('Você não tem permissão para acessar esta solicitação')

      if (solicitacao.status != 'AGUARDANDO_ANALISTA') throw new BadRequestException('Você não pode editar esta solicitação no momento.')
    }

    delete updateSolicitacaoDto['pagamento']

    const update = await xata.db.Solicitacoes.update(id, updateSolicitacaoDto)
    return 'Solicitação alterada com sucesso';
  }

  async apropriar(bearer: string, id: string) {
    const validate = await validateBearer(bearer)
    if (!validate.success) throw new UnauthorizedException()
    if (!validate.usuario) throw new UnauthorizedException()

    if (!id) throw new BadRequestException('Informe o id da solicitação')

    if (!['A', 'C'].includes(validate.usuario.type)) throw new BadRequestException('Você não tem permissão para acessar esta solicitação')

    const solicitacao = await xata.db.Solicitacoes
      .filter({
        id: id
      })
      .getFirst()
    
    if (!solicitacao) throw new BadRequestException('Solicitação não encontrada')

    if (solicitacao.status != 'AGUARDANDO_ANALISTA') throw new BadRequestException('Você não pode apropriar essa solicitação pois ela não está no status "Aguardando Analista".')

    const alteracao = {
      colaborador: validate.usuario.id,
      status: 'VERIFICANDO_SOLICITACAO'
    }

    const update = await xata.db.Solicitacoes.update(id, alteracao)
    return 'Solicitação alterada com sucesso';
  }

  async aguardandoReuniao(bearer: string, id: string) {
    const validate = await validateBearer(bearer)
    if (!validate.success) throw new UnauthorizedException()
    if (!validate.usuario) throw new UnauthorizedException()

    if (!id) throw new BadRequestException('Informe o id da solicitação')

    if (!['A', 'C'].includes(validate.usuario.type)) throw new BadRequestException('Você não tem permissão para acessar esta solicitação')

    const solicitacao = await xata.db.Solicitacoes
      .filter({
        id: id
      })
      .getFirst()

    if (!solicitacao) throw new BadRequestException('Solicitação não encontrada')

    if (solicitacao.status != 'VERIFICANDO_SOLICITACAO') throw new BadRequestException('Você não pode apropriar essa solicitação pois ela não está no status "Verificando Solicitação".')

    const alteracao = {
      status: 'AGUARDANDO_REUNIAO'
    }

    const update = await xata.db.Solicitacoes.update(id, alteracao)
    return 'Solicitação alterada com sucesso';
  }

  async cancelar(bearer: string, id: string) {
    const validate = await validateBearer(bearer)
    if (!validate.success) throw new UnauthorizedException()
    if (!validate.usuario) throw new UnauthorizedException()

    if (!id) throw new BadRequestException('Informe o id da solicitação')

    const solicitacao = await xata.db.Solicitacoes
      .filter({
        id: id
      })
      .getFirst()

    if (!solicitacao) throw new BadRequestException('Solicitação não encontrada')

    if (!['A', 'C'].includes(validate.usuario.type)){
      if (solicitacao.usuario.id != validate.usuario.id) throw new BadRequestException('Você não tem permissão para acessar esta solicitação')
    }

    const alteracao = {
      status: 'CANCELADO'
    }

    const update = await xata.db.Solicitacoes.update(id, alteracao)
    return 'Solicitação cancelado com sucesso';
  }

  async orcar(bearer: string, orcarSolicitacaoDto: OrcarSolicitacaoDto) {
      const validate = await validateBearer(bearer)
      if (!validate.success) throw new UnauthorizedException()
      if (!validate.usuario) throw new UnauthorizedException()

      if (!['A', 'C'].includes(validate.usuario.type)) throw new BadRequestException('Você não tem permissão para acessar esta solicitação')

      const { id, valor, descricao, vencimento } = orcarSolicitacaoDto

      if (!id) throw new BadRequestException('Informe o id da solicitação')
      if (!valor) throw new BadRequestException('Informe o valor do orçamento')
      if (!descricao) throw new BadRequestException('Informe a descrição do orçamento')
      if (!vencimento) throw new BadRequestException('Informe o vencimento do orçamento')
      if (!await isValidDate(vencimento)) throw new BadRequestException('Vencimento do orçamento em formato inválido')
      

      const solicitacao = await xata.db.Solicitacoes
        .filter({
          id: id
        })
        .getFirst()

      if (!solicitacao) throw new BadRequestException('Solicitação não encontrada')
      
      const usuario = await xata.db.Users
        .filter({
          id: solicitacao.usuario.id
        })
        .getFirst()

      if (!solicitacao) throw new BadRequestException('Solicitação não encontrada')
      if (!usuario) throw new BadRequestException('Usuário não encontrado')

      if (!usuario.cgc) throw new BadRequestException('Usuário não possui CPF cadastrado.')

      let asaas_id = null
      
      if (!usuario.asaas_id){
        const response = await asaas.get(`/customers/?email=${usuario.email}`)
        if (response.data.totalCount === 0){
          const data = {
            name: usuario.fullname,
            cpfCnpj: usuario.cgc,
            email: usuario.email,
            phone: usuario.cellphone_number
          }

          const response = await asaas.post(`/customers`, data)
          asaas_id = response.data.id
        } else {
          for (let cliente of response.data.data){
            if (cliente.email === usuario.email){
              asaas_id = cliente.id
            }
          }
        }
        const recordUsuario = await xata.db.Users.update(usuario.id, {
          asaas_id: asaas_id
        })
        if (!recordUsuario) throw new BadRequestException('Houve um erro ao cadastrar o usuário no banco Asaas.')
      } else {
        asaas_id = usuario.asaas_id
      }

      const data = {
        customer: asaas_id,
        billingType: 'UNDEFINED',
        value: Number(orcarSolicitacaoDto.valor),
        dueDate: orcarSolicitacaoDto.vencimento,
        description: orcarSolicitacaoDto.descricao
      }

      const response = await asaas.post(`/payments`, data)

      const record = await xata.db.Payments.create({
        name: `Cobrança referente à solicitação ${solicitacao.id}`,
        description: orcarSolicitacaoDto.descricao,
        endDate: orcarSolicitacaoDto.vencimento,
        value: Number(orcarSolicitacaoDto.valor),
        billingType: 'UNDEFINED',
        asaas_id: response.data.id,
        status: response.data.status,
        url: response.data.invoiceUrl,
        solicitacao: solicitacao.id
      });

      const alteracao = {
        status: 'AGUARDANDO_PAGAMENTO',
      }

      const update = await xata.db.Solicitacoes.update(id, alteracao)
      return 'Solicitação orçada com sucesso';
  }

  async entrega(bearer: string, entregaSolicitacaoDto: EntregaSolicitacaoDto) {
    const validate = await validateBearer(bearer)
    if (!validate.success) throw new UnauthorizedException()
    if (!validate.usuario) throw new UnauthorizedException()

    if (!['A', 'C'].includes(validate.usuario.type)) throw new BadRequestException('Você não tem permissão para acessar esta solicitação')

    const { id, valor, descricao, vencimento, entrega_link } = entregaSolicitacaoDto

    if (!id) throw new BadRequestException('Informe o id da solicitação')
    // if (!valor) throw new BadRequestException('Informe o valor do orçamento')
    if (Number(valor) > 0){
      if (!descricao) throw new BadRequestException('Informe a descrição do orçamento')
      if (!vencimento) throw new BadRequestException('Informe o vencimento do orçamento')
      if (!await isValidDate(vencimento)) throw new BadRequestException('Vencimento do orçamento em formato inválido')
      if (!entrega_link) throw new BadRequestException('Informe o link para entrega dos arquivos')
    }

    const solicitacao = await xata.db.Solicitacoes
      .filter({
        id: id
      })
      .getFirst()

    if (!solicitacao) throw new BadRequestException('Solicitação não encontrada')

    const usuario = await xata.db.Users
      .filter({
        id: solicitacao.usuario.id
      })
      .getFirst()

    if (!solicitacao) throw new BadRequestException('Solicitação não encontrada')
    if (!usuario) throw new BadRequestException('Usuário não encontrado')

    if (!usuario.cgc) throw new BadRequestException('Usuário não possui CPF cadastrado.')

    if (Number(valor) > 0){
      let asaas_id = null
      
      if (!usuario.asaas_id){
        const response = await asaas.get(`/customers/?email=${usuario.email}`)
        if (response.data.totalCount === 0){
          const data = {
            name: usuario.fullname,
            cpf: usuario.cgc,
            email: usuario.email,
            phone: usuario.cellphone_number
          }

          const response = await asaas.post(`/customers`, data)
          asaas_id = response.data.id
        } else {
          for (let cliente of response.data.data){
            if (cliente.email === usuario.email){
              asaas_id = cliente.id
            }
          }
        }
        const recordUsuario = await xata.db.Users.update(usuario.id, {
          asaas_id: asaas_id
        })
        if (!recordUsuario) throw new BadRequestException('Houve um erro ao cadastrar o usuário no banco Asaas.')
      } else {
        asaas_id = usuario.asaas_id
      }

      const data = {
        customer: asaas_id,
        billingType: 'UNDEFINED',
        value: Number(entregaSolicitacaoDto.valor),
        dueDate: entregaSolicitacaoDto.vencimento,
        description: entregaSolicitacaoDto.descricao
      }

      const response = await asaas.post(`/payments`, data)

      const record = await xata.db.Payments.create({
        name: `Cobrança referente à solicitação ${solicitacao.id}`,
        description: entregaSolicitacaoDto.descricao,
        endDate: entregaSolicitacaoDto.vencimento,
        value: Number(entregaSolicitacaoDto.valor),
        billingType: 'UNDEFINED',
        asaas_id: response.data.id,
        status: response.data.status,
        url: response.data.invoiceUrl,
        solicitacao: solicitacao.id
      });
    }

    const alteracao = {
      status: Number(valor) > 0 ? 'AGUARDANDO_PAGAMENTO_FINAL' : 'FINALIZADA',
      entrega_link: entrega_link
    }

    const update = await xata.db.Solicitacoes.update(id, alteracao)
    return 'Solicitação entregue com sucesso';
  }

  async remove(bearer: string, id: string) {
    const validate = await validateBearer(bearer)
    if (!validate.success) throw new UnauthorizedException()
    if (!validate.usuario) throw new UnauthorizedException()

    const solicitacao = await xata.db.Solicitacoes
      .filter({
        id: id
      })
      .getFirst()

    if (!['A', 'C'].includes(validate.usuario.type)){
      if (solicitacao.usuario.id != validate.usuario.id) throw new BadRequestException('Você não tem permissão para acessar esta solicitação')
    }

    const remove = await xata.db.Solicitacoes.delete(id)

    return 'Solicitação excluída com sucesso';
  }

  async webhook(webhookBody: WebhookDto) {
    if (!webhookBody.payment) throw new BadRequestException('Pagamento não informado.')

    const Pagamento = await xata.db.Payments.filter({
      $all: [
        {
          asaas_id: webhookBody.payment.id,
        },
      ],
    }).getFirst();

    if (!Pagamento) return 'Pagamento não encontrado.'

    const record = await xata.db.Payments.update(Pagamento.id, {
      url: webhookBody.payment.invoiceUrl,
      endDate: webhookBody.payment.dueDate,
      status: webhookBody.payment.status,
      paymentDate: new Date(webhookBody.payment.paymentDate),
    });

    const recordSolicitacao = await xata.db.Solicitacoes.filter({
      id: Pagamento.solicitacao.id
    }).getFirst()

    if (webhookBody.payment.status === 'RECEIVED' || webhookBody.payment.status === 'CONFIRMED'){
      if (recordSolicitacao.status === 'AGUARDANDO_PAGAMENTO_FINAL'){
        const recordSolicitacaoUpdate = await xata.db.Solicitacoes.update(Pagamento.solicitacao.id, { status: 'FINALIZADA' });
        if (!recordSolicitacaoUpdate) return { success: false, message: 'Falha ao trocar status da solicitação.', pedido: null }
      } else {
        const recordSolicitacaoUpdate = await xata.db.Solicitacoes.update(Pagamento.solicitacao.id, { status: 'EM_ANALISE' });
        if (!recordSolicitacaoUpdate) return { success: false, message: 'Falha ao trocar status da solicitação.', pedido: null }
      }
    } else if (webhookBody.payment.status === 'OVERDUE'){
      if (recordSolicitacao.status === 'AGUARDANDO_PAGAMENTO_FINAL'){
        const recordSolicitacaoUpdate = await xata.db.Solicitacoes.update(Pagamento.solicitacao.id, { status: 'ATRASO_FINAL' });
        if (!recordSolicitacaoUpdate) return { success: false, message: 'Falha ao trocar status da solicitação.', pedido: null }
      } else {
        const recordSolicitacaoUpdate = await xata.db.Solicitacoes.update(Pagamento.solicitacao.id, { status: 'ATRASO' });
        if (!recordSolicitacaoUpdate) return { success: false, message: 'Falha ao trocar status da solicitação.', pedido: null }
      }
    } if (webhookBody.event === 'PAYMENT_DELETED'){
      if (recordSolicitacao.status === 'AGUARDANDO_PAGAMENTO_FINAL'){
        const recordSolicitacaoUpdate = await xata.db.Solicitacoes.update(Pagamento.solicitacao.id, { status: 'EM_ANALISE' });
        if (!recordSolicitacaoUpdate) return { success: false, message: 'Falha ao trocar status da solicitação.', pedido: null }
      } else {
        const recordSolicitacaoUpdate = await xata.db.Solicitacoes.update(Pagamento.solicitacao.id, { status: 'AGUARDANDO_ANALISTA' });
        if (!recordSolicitacaoUpdate) return { success: false, message: 'Falha ao trocar status da solicitação.', pedido: null }
      }
      const recordPayment = await xata.db.Payments.update(Pagamento.id, { status: 'EXCLUIDO' })
      if (!recordPayment) return { success: false, message: 'Falha ao trocar status do pagamento.', pedido: null }
    }

    if (!record)
      return {
        success: false,
        message: 'Falha ao alterar pagamento.',
        pagamento: null,
      };

    return {
      success: true,
      message: 'Pagamento alterado com sucesso.',
      pagamento: record,
    };
  }
}
