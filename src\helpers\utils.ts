const jwt = require('jsonwebtoken');

export const genRanHex = size => [...Array(size)].map(() => Math.floor(Math.random() * 16).toString(16)).join('');

export async function validateBearer (bearer: string | null) {
  try {
    if (!bearer) return { success: false, message: 'No token provided' }
    if (bearer.indexOf('Bearer ') < 0) return { success: false, message: 'No Bearer provided' }

    const decoded = jwt.verify(bearer.replace('Bearer ', ''), process.env.JWT_SECRET);
    return { success: true, message: 'Token validado.', usuario: decoded}
      
  } catch (error) {
    console.log(error)
    return { success: false, message: error }            
  }
}

export async function isValidDate(dateString) {
  const regex = /^\d{4}-\d{2}-\d{2}$/;
  if (dateString.match(regex) === null) {
      // A string não corresponde ao formato de data esperado
      return false;
  }

  // Desestrutura a string para extrair ano, mês e dia
  const [year, month, day] = dateString.split('-').map(Number);

  // Cria um objeto de data com os valores extraídos
  const date = new Date(year, month - 1, day); // O mês é indexado a partir de 0 em JavaScript

  // Verifica se a data é válida
  // Isso também verifica se a conversão de mês e dia é válida (por exemplo, evita datas como 2024-02-30)
  if (date.getFullYear() !== year || date.getMonth() + 1 !== month || date.getDate() !== day) {
      return false;
  }

  return true; // A string corresponde ao formato e representa uma data válida
}