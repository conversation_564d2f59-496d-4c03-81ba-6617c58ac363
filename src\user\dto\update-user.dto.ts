import { ApiProperty } from "@nestjs/swagger";
import { IsString, IsDate, IsOptional } from "class-validator";

export class UpdateUserDto {
  @ApiProperty()
  @IsString()
  fullname: string;

  @ApiProperty()
  @IsString()
  cellphone_number?: string;

  @ApiProperty()
  @IsString()
  cellphone_number_aux?: string;

  @ApiProperty()
  @IsString()
  gender: string;

  @ApiProperty()
  @IsDate()
  date_birth: Date;

  @ApiProperty()
  @IsString()
  cgc: string;

  @ApiProperty()
  @IsString()
  avatar_url: string;

  @ApiProperty()
  @IsString()
  date_birth_BR: string;

  @ApiProperty()
  @IsString()
  picture: string;

  @IsString()
  @IsOptional()
  last_online: Date;
}
