import { Body, Controller, Post, Headers, Get, Query, Param, Patch, Delete, Put, Res, Response, Redirect } from '@nestjs/common';
import { ApiBearerAuth, ApiOperation, ApiQuery, ApiResponse, ApiTags } from '@nestjs/swagger';
import { CreateBookDto } from './dto/create-book.dto';
import { BooksService } from './books.service';
import { Book } from './entities/book.entity';
import { UpdateBookDto } from './dto/update-book.dto';

@ApiTags('book')
@Controller('books')
export class BooksController {
    constructor(private readonly booksService: BooksService) {}

    @Post()
    @ApiBearerAuth()
    @ApiOperation({ summary: 'Criar livro' })
    @ApiResponse({
        status: 200,
        description: 'Livro cadastrado',
        type: CreateBookDto,
    })
    create(@Headers() Headers, @Body() createBookDto: CreateBookDto) {
        return this.booksService.create(Headers.authorization, createBookDto);
    }

    @Get()
    @ApiOperation({ summary: 'Buscar livros' })
    @ApiResponse({
      status: 200,
      description: 'Lista de livros',
      type: Book,
    })
    @ApiQuery({
      name: "titulo_autor",
      type: String,
      description: "Filtro",
      required: false
    })
    findAll(@Query('titulo_autor') titulo_autor) {
      return this.booksService.findAll(titulo_autor);
    }

    @Get(':id')
    @ApiOperation({ summary: 'Buscar livro específico' })
    @ApiResponse({
        status: 200,
        description: 'Livro',
        type: Book,
    })
    findOne(@Param('id') id: string) {
        return this.booksService.findOne(id);
    }   

    @Get(':id/link')
    @ApiOperation({ summary: 'Buscar link específico' })
    @ApiResponse({
        status: 302,
        description: 'Redireciona para o link específico',
    })
    @Redirect()
    async getLink(@Param('id') id: string) {
      const url = await this.booksService.getLink(id);
      return { url };
    }
 

    @Put(':id')
    @ApiOperation({ summary: 'Alterar livro' })
    @ApiBearerAuth()
    @ApiResponse({
        status: 200,
        description: 'Livro alterado',
        type: Book,
    })
    update(@Headers() Headers, @Param('id') id: string, @Body() updateBookDto: UpdateBookDto) {
        return this.booksService.update(Headers.authorization, id, updateBookDto);
    }

    @Delete(':id')
    @ApiOperation({ summary: 'Excluir livro' })
    @ApiBearerAuth()
    @ApiResponse({
        status: 200,
        description: 'Livro excluído'
    })
    remove(@Headers() Headers, @Param('id') id: string) {
        return this.booksService.remove(Headers.authorization, id);
    }
}
