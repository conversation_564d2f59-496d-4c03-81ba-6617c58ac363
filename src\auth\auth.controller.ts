// auth.controller.ts
import { Controller, Get, UseGuards, Req, Redirect } from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';
import { AuthService } from './auth.service';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';

@ApiTags('auth')
@Controller('auth')
export class AuthController {
  constructor(private readonly authService: AuthService) {}

  @Get('google')
  @UseGuards(AuthGuard('google'))
  @ApiOperation({ summary: 'OAuth Google' })
  @ApiResponse({
      status: 200,
      description: 'Logged',
  })
  // This route redirects to Google; it doesn't need a method body
  async googleAuth() {
    // Initiates the Google OAuth2 login flow
  }

  @Get('google/redirect')
  @UseGuards(AuthGuard('google'))
  @ApiOperation({ summary: 'OAuth Google' })
  @ApiResponse({
      status: 200,
      description: 'Logged',
  })
  @Redirect()
  async googleAuthRedirect(@Req() req) {
    // Handle the Google OAuth2 callback and retrieve user info
    const result = await this.authService.googleLogin(req);
    if (result.token) {
      return { url: `${process.env.HABILIS_GOOGLE_REDIRECT}${result.token}`}
    }
    return { url: process.env.HABILIS_GOOGLE_REDIRECT }
  }
}
