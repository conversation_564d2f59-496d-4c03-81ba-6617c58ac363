import { BadRequestException, Injectable, UnauthorizedException } from '@nestjs/common';
import { CreateUserDto } from './dto/create-user.dto';
import { UpdateUserDto } from './dto/update-user.dto';
import { xata } from 'src/helpers/xata';
import { LoginDto } from './dto/login.dto';
import { validateBearer } from 'src/helpers/utils';
import axios from 'axios';
import { ConfirmResetPasswordDto } from './dto/confirm-reset-password';
import { ResetPasswordDto } from './dto/reset-password';
import { RequestResetPasswordDto } from './dto/request-reset-password';
const bcrypt = require('bcrypt');
const jwt = require('jsonwebtoken');

const mailURL = 'https://api.mailersend.com/v1'
const mailToken = process.env.MAIL_TOKEN

var CryptoJS = require("crypto-js");

@Injectable()
export class UserService {
  async create(createUserDto: CreateUserDto) {
    const { email, fullname, password, cellphone_number } = createUserDto

    if (!email) throw new BadRequestException('E-mail deve ser informado')
    if (!fullname) throw new BadRequestException('Nome Completo deve ser informado')
    if (!password) throw new BadRequestException('Senha deve ser informada')

    const recordExistente = await xata.db.Users.filter({
      $any: {
        email: email.trim()
      }
    }).getAll()

    if (recordExistente.length > 0) {
      throw new BadRequestException('E-mail já está em uso')
    }

    const salt = await bcrypt.genSalt(Number(process.env.CRYPT_SALT));
    const hash = bcrypt.hashSync(password, salt);

    const recordNew = await xata.db.Users.create({
      email: email.trim(),
      fullname: fullname.trim(),
      password: hash,
      cellphone_number: cellphone_number.trim(),
      type: 'U'
    })

    if (!recordNew) {
      throw new BadRequestException('Erro ao cadastrar')
    }

    const recordCreated = await xata.db.Users
      .select(['email', 'fullname', 'cellphone_number'])
      .filter({ id: recordNew.id })
      .getFirst()

    if (!recordCreated) {
      throw new BadRequestException('Erro ao cadastrar')
    }

    return recordCreated
  }

  async findAll(bearer: string, nome: string, type: string) {

    const validate = await validateBearer(bearer)
    if (!validate.success) throw new UnauthorizedException()
    if (!validate.usuario) throw new UnauthorizedException()
    if (!['A', 'C'].includes(validate.usuario.type)) throw new UnauthorizedException()

    const records = await xata.db.Users
    .select([
      'avatar_url',
      'cellphone_number',
      'date_birth',
      'date_birth_BR',
      'email',
      'fullname',
      'gender',
      'id',
      'last_online',
      'picture',
      'type',
      'cgc',
    ])
    .filter({
      $all: {
        fullname: {
          $iContains: nome
        },
        type: {
          $is: type
        }
      }
    })
    .getAll()

    records.sort((a: any, b: any) => {
      if (a.last_online < b.last_online) return 1
      else return -1
    })

    return records
  }

  async findOne(bearer: string, id: string) {
    const validate = await validateBearer(bearer)
    if (!validate.success) throw new UnauthorizedException()
    if (!validate.usuario) throw new UnauthorizedException()
    if (!['A', 'C'].includes(validate.usuario.type)){
      if (validate.usuario.id != id) throw new UnauthorizedException()
    }
    

    const records = await xata.db.Users
    .select([
      'avatar_url',
      'cellphone_number',
      'date_birth',
      'date_birth_BR',
      'email',
      'fullname',
      'gender',
      'id',
      'last_online',
      'picture',
      'type',
      'cgc',
    ])
    .filter({
      id: id
    }).getFirst()
    return records
  }

  async update(bearer: string, id: string, updateUserDto: UpdateUserDto) {
    const validate = await validateBearer(bearer)
    if (!validate.success) throw new UnauthorizedException()
    if (!validate.usuario) throw new UnauthorizedException()
    if (!['A', 'C'].includes(validate.usuario.type)){
      if (validate.usuario.id != id) throw new UnauthorizedException()
    }

    // if (!updateUserDto.cgc) throw new BadRequestException('Informe o CPF') - Não é obrigatório até a cobrança
    if (!updateUserDto.fullname) throw new BadRequestException('Informe o nome completo')

    if (!updateUserDto.cgc) {
      const recordUnique = await xata.db.Users.filter({
        cgc: updateUserDto.cgc
      }).getFirst()

      if (recordUnique) throw new BadRequestException('CPF já está em uso em nossa plataforma')
    }

    const record = await xata.db.Users.update(id, updateUserDto)
    return record
  }

  async remove(bearer: string, id: string) {
    const validate = await validateBearer(bearer)
    if (!validate.success) throw new UnauthorizedException()
    if (!validate.usuario) throw new UnauthorizedException()
    if (!['A', 'C'].includes(validate.usuario.type)) throw new UnauthorizedException()

    const record = await xata.db.Users.delete(id)
    return record
  }

  async login(loginDto: LoginDto) {
    if (!loginDto.email) throw new BadRequestException('E-mail não informado.')
    if (!loginDto.password) throw new BadRequestException('Senha não informada.')

    const hashCrypto = CryptoJS.SHA256(process.env.VUE_APP_BCRYPT_SECRET + loginDto.password)

    const user = await xata.db.Users
        .filter({
            $all: [
                {
                email: loginDto.email,
                }
            ],
        })
        .getFirst();

    if (!user) throw new BadRequestException('Combinação Usuário/Senha não encontrada.')

    let usuario = {}
    
    if (user.password.startsWith('[')){
      if (user.password != JSON.stringify(hashCrypto.words)) throw new BadRequestException('Combinação Usuário/Senha não encontrada.')
    } else {
      const match = await bcrypt.compare(loginDto.password, user.password);
      if (!match) throw new BadRequestException('Combinação Usuário/Senha não encontrada.')
    }

    usuario = {
      cellphone_number: user.cellphone_number,
      cellphone_number_aux: user.cellphone_number_aux,
      date_birth: user.date_birth,
      email: user.email,
      fullname: user.fullname,
      gender: user.gender,
      id: user.id,
      type: user.type
    }
          
    let token = jwt.sign(usuario, process.env.JWT_SECRET, { expiresIn: '30 days' });

    const record = await xata.db.Users.update(user.id, {
      last_online: new Date()
    });

    if (!record) throw new BadRequestException('Houve um erro ao realizar o login')

    return { message: 'Login efetuado com sucesso.', usuario: usuario, token: token }
  }

  makeid(length) {
    let result = '';
    const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    const charactersLength = characters.length;
    let counter = 0;
    while (counter < length) {
      result += characters.charAt(Math.floor(Math.random() * charactersLength));
      counter += 1;
    }
    return result;
  }

  async sendMailResetPassword(requestResetPasswordDto: RequestResetPasswordDto) {

    const { email } = requestResetPasswordDto

      if (!email.trim().toLowerCase()) throw new BadRequestException('E-mail não informado.')

      const user = await xata.db.Users
      .filter({ email: email.trim().toLowerCase() })
      .getFirst();
      if (!user) throw new BadRequestException('Usuário não encontrado.')

      const code = await this.makeid(5)

      const data = {
          from: {
            email: "<EMAIL>",
            name: "Habilis"
          },
          to: [
            {
              email: user.email,
              name: user.fullname
            }
          ],
          subject: "{$company} - Alteração de Senha",
          text: "Olá, {$fullname}, seu código para alteração de senha é: {$code}.",
          html: "Olá, {$fullname}, seu código para alteração de senha é <b>{$code}.</b>",
          variables: [
            {
              email: user.email,
              substitutions: [
                {
                  var: "company",
                  value: "Habilis"
                },
                {
                  var: "code",
                  value: code
                },
                {
                  var: "fullname",
                  value: user.fullname
                }
              ]
            }
          ]
        }

      const config = {
          headers: { Authorization: `Bearer ${mailToken}` }
      };


      const response = await axios.post(`${mailURL}/email`, data, config)
      
      const record = await xata.db.Users.update(user.id, {
        password_code: code
      });

      return 'Email enviado com sucesso.'
  }

  async resetPassword(resetPasswordDto: ResetPasswordDto) {

      if (!resetPasswordDto.email) return { success: false, message: 'E-mail não informado.'}
      if (!resetPasswordDto.code) return { success: false, message: 'Código não informado.'}
      if (!resetPasswordDto.password) return { success: false, message: 'Nova Senha não informada.'}

      const user = await xata.db.Users
      .filter({
          $all: [
              {
              email: resetPasswordDto.email,
              }
          ],
      })
      .getFirst();
      if (!user) return { success: false, message: 'Usuário não encontrado.', usuario: null}

      if (resetPasswordDto.code != user.password_code) return { success: false, message: 'Código Incorreto', usuario: null}

      const salt = await bcrypt.genSalt(Number(process.env.CRYPT_SALT));
      const hash = bcrypt.hashSync(resetPasswordDto.password, salt);

      const record = await xata.db.Users.update(user.id, {
        password_code: null,
        password: hash
      });

      const data = {
        from: {
          email: "<EMAIL>",
          name: "Habilis"
        },
        to: [
          {
            email: user.email,
            name: user.fullname
          }
        ],
        subject: "{$company} - Senha alterada com sucesso!",
        text: "Olá, {$fullname}, sua senha foi alterada com sucesso!.",
        html: "Olá, {$fullname}, sua senha foi alterada com sucesso!.",
        variables: [
          {
            email: user.email,
            substitutions: [
              {
                var: "company",
                value: "Habilis"
              },
              {
                var: "fullname",
                value: user.fullname
              }
            ]
          }
        ]
      }

    const config = {
        headers: { Authorization: `Bearer ${mailToken}` }
    };


    const response = await axios.post(`${mailURL}/email`, data, config)

    return 'Senha alterada com sucesso.'
  }

  async validate(token: any, coords?: any) {
    if (coords){
      const AcessoWithCoord = await xata.db.Acessos.filter({
        latitude: coords.latitude,
        longitude: coords.longitude
      }).getFirst()
  
      if (!AcessoWithCoord){
        const createAcesso = {...coords, ...{quantidade: 1}}
        await xata.db.Acessos.create(createAcesso)
      } else {
        const updateAcesso = {
          quantidade: AcessoWithCoord.quantidade + 1
        }
        await xata.db.Acessos.update(AcessoWithCoord.id, updateAcesso)
      }
    } else {
      const AcessoWithCoord = await xata.db.Acessos.filter({
        latitude: {
          $is: null
        },
        longitude: null
      }).getFirst()
  
      if (!AcessoWithCoord){
        const createAcesso = {...{
          latitude: null,
          longitude: null
        }, ...{quantidade: 1}}
        await xata.db.Acessos.create(createAcesso)
      } else {
        const updateAcesso = {
          quantidade: AcessoWithCoord.quantidade + 1
        }
        await xata.db.Acessos.update(AcessoWithCoord.id, updateAcesso)
      }
    }
    
    if (!token) throw new UnauthorizedException('Token não informado.')

    try {
      let decoded = jwt.verify(token, process.env.JWT_SECRET);
      decoded.token = token
      console.log('🤬 ~ UserService ~ validate ~ decoded:', decoded)
      return { success: true, message: 'Token validado.', usuario: decoded}
    }
    catch (error) {
      console.log(error.message)
      return { success: false, message: error.message, usuario: null}
    } 
  }

  async findAcessos(){
    return { success: true, message: 'Acessos encontrados', acessos: await xata.db.Acessos.getAll()}
  }
}
