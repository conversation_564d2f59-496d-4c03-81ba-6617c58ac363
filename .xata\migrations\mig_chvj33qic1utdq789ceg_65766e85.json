{"id": "mig_chvj33qic1utdq789ceg", "checksum": "1:65766e859c64fbe3ed36b8ce786c95a45f23882b983c1afc7170078c8b05ea40", "operations": [{"addTable": {"table": "Posts"}}, {"addTable": {"table": "Users"}}, {"addColumn": {"column": {"name": "title", "type": "string"}, "table": "Posts"}}, {"addColumn": {"column": {"name": "labels", "type": "multiple"}, "table": "Posts"}}, {"addColumn": {"column": {"name": "slug", "type": "string"}, "table": "Posts"}}, {"addColumn": {"column": {"name": "text", "type": "text"}, "table": "Posts"}}, {"addColumn": {"column": {"name": "author", "type": "link", "link": {"table": "Users"}}, "table": "Posts"}}, {"addColumn": {"column": {"name": "createdAt", "type": "datetime"}, "table": "Posts"}}, {"addColumn": {"column": {"name": "views", "type": "int"}, "table": "Posts"}}, {"addColumn": {"column": {"name": "name", "type": "string"}, "table": "Users"}}, {"addColumn": {"column": {"name": "email", "type": "email"}, "table": "Users"}}, {"addColumn": {"column": {"name": "bio", "type": "text"}, "table": "Users"}}]}