import 'dotenv/config';

import { MailerSend, EmailParams, Sender, Recipient } from "mailersend";
import { OrcarSolicitacaoDto } from 'src/solicitacao/dto/orcar-solicitacao.dto';

const mailerSend = new MailerSend({
  apiKey: process.env.MAIL_TOKEN,
});

const sentFrom = new Sender("<EMAIL>", "Habilis");

export async function sendMailRequestPayment(email: string, nome: string, orcarSolicitacaoDto: OrcarSolicitacaoDto, invoiceUrl: string): Promise<boolean>{

  const recipients = [ new Recipient(email, nome) ];
  
  const personalization = [
    {
      email: email,
      data: {
        name: nome,
        orcamento: {
          descricao: orcarSolicitacaoDto.descricao,
          valor: orcarSolicitacaoDto.valor,
          vencimento: orcarSolicitacaoDto.vencimento,
        },
        invoiceUrl: invoiceUrl
      },
    }
  ];

  const emailParams = new EmailParams()
    .setFrom(sentFrom)
    .setTo(recipients)
    .setReplyTo(sentFrom)
    .setPersonalization(personalization)
    .setSubject("Habilis - Aguardando Pagamento")
    .setTemplateId('zr6ke4nzvv34on12');


    const response = await mailerSend.email.send(emailParams);

    if (response.statusCode === 202){
      return true
    }
    return false

}

export async function sendMailNewRequest(): Promise<boolean>{

  const recipients = [
    new Recipient('<EMAIL>', 'Luan Vieira'),
    new Recipient('<EMAIL>', 'Luan Vieira'),
    new Recipient('<EMAIL>', 'Rodrigo Fioravanti Pereira'),
    new Recipient('<EMAIL>', 'Rodrigo Fioravanti Pereira'),
    new Recipient('<EMAIL>', 'Felipe Schroeder de Oliveira'),
  ];
  
  const personalization = [
  ];

  const emailParams = new EmailParams()
    .setFrom(sentFrom)
    .setTo(recipients)
    .setReplyTo(sentFrom)
    .setPersonalization(personalization)
    .setSubject("Habilis - Nova Solicitação")
    .setTemplateId('351ndgww55ngzqx8');


    const response = await mailerSend.email.send(emailParams);

    if (response.statusCode === 202){
      return true
    }
    return false

}