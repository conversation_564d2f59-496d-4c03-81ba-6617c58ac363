import { ApiProperty } from "@nestjs/swagger";
import { IsDate, IsN<PERSON>ber, IsString } from "class-validator";

export class Payment {
    @IsString()
    @ApiProperty()
    id: string;

    @IsString()
    @ApiProperty()
    asaas_id: string;

    @IsString()
    @ApiProperty()
    name: string;

    @IsString()
    @ApiProperty()
    description: string;

    @IsDate()
    @ApiProperty()
    endDate: string;

    @IsNumber()
    @ApiProperty()
    value: number;

    @IsString()
    @ApiProperty()
    billingType: string;

    @IsString()
    @ApiProperty()
    chargeType: string;
    
    @IsNumber()
    @ApiProperty()
    dueDateLimitDays: number;

    @IsString()
    @ApiProperty()
    url: string;

    @IsString()
    @ApiProperty()
    status: string;

    @IsDate()
    @ApiProperty()
    paymentDate: string;

}
