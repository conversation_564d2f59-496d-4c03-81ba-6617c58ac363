import { Injectable, UnauthorizedException } from '@nestjs/common';
import { Socket } from 'socket.io';
import { validateBearer } from 'src/helpers/utils';
import { xata } from 'src/helpers/xata';

@Injectable()
export class SocketService {
  private readonly connectedClients: Map<string, Socket> = new Map();

  async handleConnection(socket: Socket): Promise<void> {
    const clientId = socket.id;
    if (!socket.handshake.headers.authorization) return

    const validate = await validateBearer(socket.handshake.headers.authorization)
    if (!validate.success || !validate.usuario) return

    this.connectedClients.set(validate.usuario.id, socket);

    socket.on('disconnect', () => {
      this.connectedClients.delete(clientId);
    });
  }
//socket: Socket, 
//, argument: any
async handleChatEvent(socket: Socket, argument: any ) {
  const EVENT = 'chat'
    
  if (!socket.handshake.headers.authorization){
    socket.emit(EVENT, 'No Token Provided')
    return
  }

  const validate = await validateBearer(socket.handshake.headers.authorization)
  if (!validate.success || !validate.usuario) {
    socket.emit(EVENT, validate.message)
    return
  }
  
  await xata.db.Chat.create({
    user: argument.usuario.id,
    collab: argument.collab ? argument.collab.id : null,
    datetime: new Date(),
    message: argument.message
  })

  if (argument.collab.id){ //Colaborador mandou mensagem
    const recordCollab = await xata.db.Users.select(['fullname']).filter({ id: argument.collab.id }).getFirst()
    argument.collab.fullname = recordCollab.fullname
    this.connectedClients.forEach((client, id) => {
      if (argument.usuario.id === id){ //Para um usuário específico
        console.log('mandando mensagem para o usuario')
        client.emit('chat', argument)
      }
    })
  } else { //Usuário mandou mensagem
    const recordUser = await xata.db.Users.select(['fullname']).filter({ id: argument.usuario.id }).getFirst()
    argument.usuario.fullname = recordUser.fullname
    const recordChat = await xata.db.Chat.filter({
      user: argument.usuario.id
    }).getAll()
    const idsSent = []
    for (let chat of recordChat){
      if (chat){
        if (chat.collab){
          if (chat.collab.id){
            this.connectedClients.forEach((client, id) => {
            //Para todos os colaboradores ouvindo
              if (!idsSent.includes(id)){
                idsSent.push(id)
                console.log('mandando mensagem para o colaborador')
                client.emit('chat', argument)
              }
            })
          }
        }
      }
    }
  }
}
  
}