import { ApiProperty } from "@nestjs/swagger";
import { IsString, IsOptional, IsN<PERSON>ber } from "class-validator";

export class CreateBookDto {
  @ApiProperty()
  @IsString()
  titulo: string;

  @ApiProperty()
  @IsString()
  idioma: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  dt_publicacao?: string;

  @ApiProperty()
  @IsNumber()
  @IsOptional()
  nro_paginas?: number;

  @ApiProperty()
  @IsString()
  autor: string;

  @ApiProperty()
  @IsString()
  resumo: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  editora?: string;

  @ApiProperty()
  @IsString()
  link: string;

  @ApiProperty()
  @IsString()
  url_imagem: string;
}
