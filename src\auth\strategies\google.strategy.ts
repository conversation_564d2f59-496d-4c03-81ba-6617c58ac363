// auth/strategies/google.strategy.ts
import { Injectable } from '@nestjs/common';
import { PassportStrategy } from '@nestjs/passport';
import { Strategy, VerifyCallback } from 'passport-google-oauth20';

@Injectable()
export class GoogleStrategy extends PassportStrategy(Strategy, 'google') {
  constructor() {
    super({
      clientID: process.env.GOOGLE_CLIENT_ID,      // from Google Developer Console
      clientSecret: process.env.GOOGLE_CLIENT_SECRET,  // from Google Developer Console
      callbackURL: process.env.GOOGLE_REDIRECT,
      scope: ['email', 'profile'],  // the information you want from the user
    });
  }

  async validate(
    accessToken: string,
    refreshToken: string,
    profile: any,
    done: VerifyCallback,
  ): Promise<any> {
    // Here, you can save the user data to a database or check if the user already exists.
    // For demonstration, we’ll attach the Google profile to the user object.

    const { name, emails, photos } = profile;
    const user = {
      email: emails?.[0]?.value,
      firstName: name?.givenName,
      lastName: name?.familyName,
      picture: photos?.[0]?.value,
      accessToken,
    };
    done(null, user);
  }
}
