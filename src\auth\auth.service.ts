// auth.service.ts
import { BadRequestException, Injectable } from '@nestjs/common';
import { xata } from 'src/helpers/xata';
import { UpdateUserDto } from 'src/user/dto/update-user.dto';
const jwt = require('jsonwebtoken');

@Injectable()
export class AuthService {
  async googleLogin(req) {
    const { user } = req;
    if (!user) {
      return { success: false, message: 'User not found', usuario: null, token: null };
    }

    let UserFound = await xata.db.Users
        .select([
          'avatar_url',
          'cellphone_number',
          'date_birth',
          'date_birth_BR',
          'email',
          'fullname',
          'gender',
          'id',
          'last_online',
          'picture',
          'type',
          'cgc',
        ])
        .filter({
          email: user.email
        }).getFirst()

    if (!UserFound) {
      const recordNew = await xata.db.Users.create({
        email: user.email,
        fullname: user.firstName + ' ' + user.lastName,
        password: null,
        cellphone_number: null,
        type: 'U'
      })
  
      if (!recordNew) {
        throw new BadRequestException('Erro ao cadastrar')
      }
  
      const recordCreated = await xata.db.Users
        .select(['email', 'fullname', 'cellphone_number'])
        .filter({ id: recordNew.id })
        .getFirst()
  
      if (!recordCreated) {
        throw new BadRequestException('Erro ao cadastrar')
      }

      UserFound = await xata.db.Users
        .select([
          'avatar_url',
          'cellphone_number',
          'date_birth',
          'date_birth_BR',
          'email',
          'fullname',
          'gender',
          'id',
          'last_online',
          'picture',
          'type',
          'cgc',
        ])
        .filter({
          email: user.email
        }).getFirst()
    }

    let updateUserDto: UpdateUserDto = {
      ...UserFound,
      picture: UserFound.picture?.url,
      avatar_url: user.picture,
      last_online: new Date()
    };

    const record = await xata.db.Users.update(UserFound.id, updateUserDto)

    const usuario = {
      cellphone_number: record.cellphone_number,
      cellphone_number_aux: record.cellphone_number_aux,
      date_birth: record.date_birth,
      email: record.email,
      fullname: record.fullname,
      gender: record.gender,
      id: record.id,
      type: record.type
    }
          
    let token = jwt.sign(usuario, process.env.JWT_SECRET, { expiresIn: '30 days' });

    if (!record) throw new BadRequestException('Houve um erro ao realizar o login')

    return { success: true, message: 'Login efetuado com sucesso.', usuario: usuario, token: token }
  }
}
