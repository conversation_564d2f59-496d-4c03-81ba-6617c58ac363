import { <PERSON>, Get, Headers, Body, Patch, Param, Delete } from '@nestjs/common';
import { ChatService } from './chat.service';
import { ApiTags, ApiBearerAuth, ApiOperation } from '@nestjs/swagger';

@ApiTags('chat')
@Controller('chat')
export class ChatController {
  constructor(private readonly chatService: ChatService) {}

  @Get(':id')
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Busca o histórico de mensagens' })
  findAll(@Headers() Headers,  @Param('id') id: string) {
    return this.chatService.findAll(Headers.authorization, id);
  }

  @Get('/not_seen/:id')
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Busca o histórico de mensagens' })
  countNotSeen(@Headers() Headers,  @Param('id') id: string) {
    return this.chatService.countNotSeen(Headers.authorization, id);
  }
}
