import { Controller, Get, Post, Body, Patch, Param, Delete, Headers, HttpCode, BadRequestException } from '@nestjs/common';
import { SolicitacaoService } from './solicitacao.service';
import { CreateSolicitacaoDto } from './dto/create-solicitacao.dto';
import { UpdateSolicitacaoDto } from './dto/update-solicitacao.dto';
import { ApiBearerAuth, ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';
import { OrcarSolicitacaoDto } from './dto/orcar-solicitacao.dto';
import { WebhookDto } from './dto/webhook.dto';
import { EntregaSolicitacaoDto } from './dto/entrega-solicitacao.dto';

@ApiTags('solicitacao')
@Controller('solicitacao')
export class SolicitacaoController {
  constructor(private readonly solicitacaoService: SolicitacaoService) {}

  @Post()
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Criar nova Solicitação' })
  create(@Headers() Headers, @Body() createSolicitacaoDto: CreateSolicitacaoDto) {
    return this.solicitacaoService.create(Headers.authorization, createSolicitacaoDto);
  }

  @Get()
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Buscar todas as solicitações' })
  findAll(@Headers() Headers) {
    return this.solicitacaoService.findAll(Headers.authorization);
  }

  @Get(':id')
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Buscar uma solicitação específica' })
  findOne(@Headers() Headers, @Param('id') id: string) {
    return this.solicitacaoService.findOne(Headers.authorization, id);
  }

  @Patch(':id')
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Alterar uma Solicitação' })
  update(@Headers() Headers, @Param('id') id: string, @Body() updateSolicitacaoDto: UpdateSolicitacaoDto) {
    return this.solicitacaoService.update(Headers.authorization, id, updateSolicitacaoDto);
  }

  @Patch('apropriar/:id')
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Apropriar' })
  apropriar(@Headers() Headers, @Param('id') id: string) {
    return this.solicitacaoService.apropriar(Headers.authorization, id);
  }

  @Patch('reuniao/:id')
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Aguardando reunião' })
  aguardandoReuniao(@Headers() Headers, @Param('id') id: string) {
    return this.solicitacaoService.aguardandoReuniao(Headers.authorization, id);
  }

  @Patch('cancelar/:id')
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Cancelar uma Solicitação' })
  cancelar(@Headers() Headers, @Param('id') id: string) {
    return this.solicitacaoService.cancelar(Headers.authorization, id);
  }

  @Patch('orcar/:id')
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Orçar uma Solicitação' })
  orcar(@Headers() Headers, @Body() orcarSolicitacaoDto: OrcarSolicitacaoDto) {
    return this.solicitacaoService.orcar(Headers.authorization, orcarSolicitacaoDto);
  }

  @Patch('entrega/:id')
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Entregar uma Solicitação' })
  entrega(@Headers() Headers, @Body() entregaSolicitacaoDto: EntregaSolicitacaoDto) {
    return this.solicitacaoService.entrega(Headers.authorization, entregaSolicitacaoDto);
  }

  @Patch('reabrir/:id')
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Reabrir uma Solicitação' })
  reabrir(@Headers() Headers, @Param('id') id: string) {
    return this.solicitacaoService.reabrir(Headers.authorization, id);
  }

  @Delete(':id')
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Excluir uma Solicitação' })
  remove(@Headers() Headers, @Param('id') id: string) {
    return this.solicitacaoService.remove(Headers.authorization, id);
  }

  @Post('webhook')
  @HttpCode(200)
  @ApiOperation({ summary: 'Webhook pagamento' })
  @ApiResponse({
    status: 200,
    description: 'Ok',
  })
  async webhook(@Headers() Headers, @Body() webhookBody: WebhookDto) {
    const validate = await this.validateWebhookAsaas(Headers['asaas-access-token'])
    if (!validate.status) throw new BadRequestException(validate.message)
    
    return await this.solicitacaoService.webhook(webhookBody)
  }

  async validateWebhookAsaas (token: string) {
    try {
      if (!token) return { status: false, token: null, message: 'No token provided' }
      if (token != process.env.CRYPT_SALT) return { status: false, token: null, message: 'Token incorreto' }
      return { status: true, message: 'Ok' }
        
    } catch (error) {
      console.log(error)
      return { status: false, message: error }            
    }
  }
}
