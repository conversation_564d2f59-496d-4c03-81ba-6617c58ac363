import { Injectable, UnauthorizedException, BadRequestException } from '@nestjs/common';
import { validateBearer } from 'src/helpers/utils';
import { xata } from 'src/helpers/xata';

@Injectable()
export class ChatService {
  async findAll(bearer: string, user_id: string) {
    const validate = await validateBearer(bearer)
    if (!validate.success) throw new UnauthorizedException()
    if (!validate.usuario) throw new UnauthorizedException()

    let UsuarioId = validate.usuario.id

    const IS_ADMIN = (user_id != UsuarioId)

    if (IS_ADMIN){
      if (!['A', 'C'].includes(validate.usuario.type)) throw new BadRequestException('Você não tem permissão para acessar este conteúdo')
      UsuarioId = user_id
    }

    const messages = await xata.db.Chat
      .select(['*', 'collab.fullname', 'user.fullname'])
      .filter({
        user: UsuarioId
      })
      .getAll()

    if (IS_ADMIN){
      for (let message of messages){
        const recordUpdate = await xata.db.Chat.update(message.id, {
          seen: true
        })
      }
    }

    return messages
  }

  async countNotSeen(bearer: string, user_id: string) {
    const validate = await validateBearer(bearer)
    if (!validate.success) throw new UnauthorizedException()
    if (!validate.usuario) throw new UnauthorizedException()

    let UsuarioId = validate.usuario.id

    const IS_ADMIN = (user_id != UsuarioId)

    if (IS_ADMIN){
      if (!['A', 'C'].includes(validate.usuario.type)) throw new BadRequestException('Você não tem permissão para acessar este conteúdo')
      UsuarioId = user_id
    }

    const messages = await xata.db.Chat
      .select(['*', 'collab.fullname', 'user.fullname'])
      .filter({
        user: UsuarioId,
        seen: false
      })
      .getAll()

    return messages.length
  }
}
