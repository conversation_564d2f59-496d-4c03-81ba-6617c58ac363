import { BadRequestException, Injectable, UnauthorizedException } from '@nestjs/common';
import { isValidDate, validateBearer } from 'src/helpers/utils';
import { xata } from 'src/helpers/xata';

@Injectable()
export class PaymentsService {
  async findAll(bearer: string) {
    const validate = await validateBearer(bearer)
    if (!validate.success) throw new UnauthorizedException()
    if (!validate.usuario) throw new UnauthorizedException()

    if (['A', 'C'].includes(validate.usuario.type)){
      const pagamentos = await xata.db.Payments.getAll()
      return pagamentos
    } else {
      const pagamentos = await xata.db.Payments
      .filter({
        "solicitacao.usuario": validate.usuario.id
      })
      .getAll()
      return pagamentos
    }
  }

  async findOne(bearer: string, id: string) {
    const validate = await validate<PERSON>earer(bearer)
    if (!validate.success) throw new UnauthorizedException()
    if (!validate.usuario) throw new UnauthorizedException()

    if (!id) throw new BadRequestException('Informe o id do pagamento')

    if (['A', 'C'].includes(validate.usuario.type)){
      const pagamentos = await xata.db.Payments
        .select(['*', 'solicitacao.*', 'solicitacao.usuario.*'])
        .filter({
          "solicitacao.id": id
        })        
        .getAll()
      return pagamentos
    } else {
      const pagamentos =  await xata.db.Payments
      .select(['*', 'solicitacao.*', 'solicitacao.usuario.*'])
      .filter({
        "solicitacao.id": id,
        "solicitacao.usuario.id": validate.usuario.id
      })
      .getAll()
      return pagamentos
    }
  }
}
