import { Controller, Get, Post, Body, Patch, Param, Delete, Query, Headers } from '@nestjs/common';
import { UserService } from './user.service';
import { CreateUserDto } from './dto/create-user.dto';
import { UpdateUserDto } from './dto/update-user.dto';
import { ApiOperation, ApiQuery, ApiResponse, ApiTags, ApiBearerAuth } from '@nestjs/swagger';
import { User } from './entities/user.entity';
import { LoginDto } from './dto/login.dto';
import { ConfirmResetPasswordDto } from './dto/confirm-reset-password';
import { ResetPasswordDto } from './dto/reset-password';
import { RequestResetPasswordDto } from './dto/request-reset-password';
@ApiTags('user')
@Controller('user')
export class UserController {
  constructor(private readonly userService: UserService) {}

  @Post('auth')
  @ApiOperation({ summary: 'Autenticação' })
  @ApiResponse({
    status: 200,
    description: 'Usuário autenticado',
    type: LoginDto,
  })
  login(@Body() loginDto: LoginDto) {
    return this.userService.login(loginDto);
  }

  @Post()
  @ApiOperation({ summary: 'Criar usuário' })
  @ApiResponse({
    status: 200,
    description: 'Usuário cadastrado',
    type: CreateUserDto,
  })
  create(@Body() createUserDto: CreateUserDto) {
    return this.userService.create(createUserDto);
  }

  @Get()
  @ApiOperation({ summary: 'Buscar usuários' })
  @ApiBearerAuth()
  @ApiResponse({
    status: 200,
    description: 'Lista de usuários',
    type: User,
  })
  @ApiQuery({
    name: "fullname",
    type: String,
    description: "Nome do usuário",
    required: false
  })
  @ApiQuery({
    name: "type",
    type: String,
    description: "Tipo do usuário",
    required: false
  })
  findAll(@Headers() Headers, @Query('fullname') fullname?: string, @Query('type') type?: string) {
    return this.userService.findAll(Headers.authorization, fullname, type);
  }

  @Get(':id')
  @ApiOperation({ summary: 'Buscar usuário específico' })
  @ApiBearerAuth()
  @ApiResponse({
    status: 200,
    description: 'Usuário',
    type: User,
  })
  findOne(@Headers() Headers, @Param('id') id: string) {
    if (id === 'acessos'){
      return this.userService.findAcessos()
    }
    return this.userService.findOne(Headers.authorization, id);
  }

  @Patch(':id')
  @ApiOperation({ summary: 'Alterar usuário' })
  @ApiBearerAuth()
  @ApiResponse({
    status: 200,
    description: 'Usuário alterado',
    type: User,
  })
  update(@Headers() Headers, @Param('id') id: string, @Body() updateUserDto: UpdateUserDto) {
    return this.userService.update(Headers.authorization, id, updateUserDto);
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Excluir usuário' })
  @ApiBearerAuth()
  @ApiResponse({
    status: 200,
    description: 'Usuário excluído'
  })
  remove(@Headers() Headers, @Param('id') id: string) {
    return this.userService.remove(Headers.authorization, id);
  }

  @Post('password/reset/request')
  @ApiOperation({ summary: 'Pedir e-mail de alteração de senha' })
  @ApiResponse({
    status: 200,
    description: 'E-mail enviado'
  })
  resetRequest(@Body() requestResetPasswordDto: RequestResetPasswordDto) {
    return this.userService.sendMailResetPassword(requestResetPasswordDto);
  }

  @Post('password/reset')
  @ApiOperation({ summary: 'Altera a senha' })
  @ApiResponse({
    status: 200,
    description: 'Senha alterada'
  })
  resetPassword(@Body() resetPasswordDto: ResetPasswordDto) {
    return this.userService.resetPassword(resetPasswordDto)
  }

  @Post('/validate')
    @ApiOperation({ summary: 'Validar sessão' })
    @ApiResponse({
      status: 200,
      description: 'Sessão validada'
    })
  async validate(@Body() body: any) {
    const { token, coords } = body
    return await this.userService.validate(token, coords)
  }

  @Post('/validate-token')
    @ApiOperation({ summary: 'Validar sessão' })
    @ApiResponse({
      status: 200,
      description: 'Sessão validada'
    })
  async validateToken(@Body() body: any) {
    const { token } = body
    return await this.userService.validate(token, null)
  }
}
