import { <PERSON>, Get, Headers, Param } from '@nestjs/common';
import { PaymentsService } from './payments.service';
import { ApiTags, ApiBearerAuth, ApiOperation } from '@nestjs/swagger';
@ApiTags('payments')
@ApiBearerAuth()
@Controller('payments')
export class PaymentsController {
  constructor(private readonly paymentsService: PaymentsService) {}

  @Get()
  @ApiOperation({ summary: 'Buscar todos os pagamentos' })
  findAll(@Headers() Headers) {
    return this.paymentsService.findAll(Headers.authorization);
  }

  @Get(':id')
  @ApiOperation({ summary: 'Buscar apenas um pagamento' })
  findOne(@Headers() Headers, @Param('id') id: string) {
    return this.paymentsService.findOne(Headers.authorization, id);
  }
}
