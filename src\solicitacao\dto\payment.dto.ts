import { ApiProperty } from '@nestjs/swagger';
import { IsString } from 'class-validator';

export class PaymentDto {
  @ApiProperty()
  @IsString()
  readonly object: string;

  @ApiProperty()
  @IsString()
  readonly id: string;

  @ApiProperty()
  @IsString()
  readonly dateCreated: string;

  @ApiProperty()
  @IsString()
  readonly customer: string;

  @ApiProperty()
  @IsString()
  readonly paymentLink: string | null;

  @ApiProperty()
  @IsString()
  readonly value: number;

  @ApiProperty()
  @IsString()
  readonly description: string;
  
  @ApiProperty()
  @IsString()
  readonly billingType: string;
  
  @ApiProperty()
  @IsString()
  readonly confirmedDate: string;
  
  @ApiProperty()
  @IsString()
  readonly pixTransaction: string;
  
  @ApiProperty()
  @IsString()
  readonly status: string;
  
  @ApiProperty()
  @IsString()
  readonly dueDate: string;
  
  @ApiProperty()
  @IsString()
  readonly originalDueDate: string;
  
  @ApiProperty()
  @IsString()
  readonly paymentDate: string;
  
  @ApiProperty()
  @IsString()
  readonly clientPaymentDate: string;
  
  @ApiProperty()
  @IsString()
  readonly invoiceUrl: string;
  
  @ApiProperty()
  @IsString()
  readonly invoiceNumber: string;
  
  @ApiProperty()
  @IsString()
  readonly creditDate: string;
  
  @ApiProperty()
  @IsString()
  readonly estimatedCreditDate: string;
  
  @ApiProperty()
  @IsString()
  readonly transactionReceiptUrl: string;
  
  @ApiProperty()
  @IsString()
  readonly nossoNumero: string;
  
  @ApiProperty()
  @IsString()
  readonly bankSlipUrl: string;
}