import { ApiProperty } from "@nestjs/swagger"
import { IsDate, IsN<PERSON>ber, IsString } from "class-validator"

export class Solicitacao {
  @IsString()
  @ApiProperty()
  id:string

  @IsString()
  @ApiProperty()
  titulo: string

  @IsString()
  @ApiProperty()
  link_dados: string

  @IsString()
  @ApiProperty()
  status: string

  @IsString()
  @ApiProperty()
  objetivo_geral: string

  @IsString()
  @ApiProperty()
  objetivos_especificos: string

  @IsString()
  @ApiProperty()
  hipoteses: string

  @IsString()
  @ApiProperty()
  variaveis: string

  @IsString()
  @ApiProperty()
  detalhamentos: string

  @IsString()
  @ApiProperty()
  instrumentos: string

  @IsString()
  @ApiProperty()
  instrumentos_referencias: string

  @IsString()
  @ApiProperty()
  metodologia: string

  @IsString()
  @ApiProperty()
  viabilidade_descricao: string

  @IsString()
  @ApiProperty()
  viabilidade_link: string

  @IsNumber()
  @ApiProperty()
  viabilidade_valor: number

  @IsDate()
  @ApiProperty()
  viabilidade_prazo: Date

  @IsString()
  @ApiProperty()
  tipo: string

  @IsString()
  @ApiProperty()
  observacoes: string

  @IsString()
  @ApiProperty()
  entrega_link: string;
}
